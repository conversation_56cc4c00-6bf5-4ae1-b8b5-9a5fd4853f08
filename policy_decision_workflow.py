from llama_index.core.workflow import (
    Workflow,
    StartEvent,
    StopEvent,
    Event,
    context,
    step,
    Context
)
from typing import List, Dict
import asyncio
from tavily import AsyncTavilyClient
from dotenv import load_dotenv
import os
from llama_index.llms.openrouter import OpenRouter
import prompts
import json
from models import Section, Report
from rag_client import RAGClient
import warnings
warnings.filterwarnings("ignore", category=ResourceWarning)
from llama_index.llms.ollama import Ollama
from llama_index.llms.deepseek import DeepSeek
from llama_index.core.llms import ChatMessage



# Load environment variables
load_dotenv()

QUERY_GENERATION_COUNT = int(os.environ.get("QUERY_GENERATION_COUNT",os.getenv("QUERY_GENERATION_COUNT", 1)))
MAX_SEARCH_COUNT = int(os.environ.get("MAX_SEARCH_COUNT",os.getenv("MAX_SEARCH_COUNT", 1)))
SERVICE_UNAVAILABLE = "服务当前不可用,请稍后再试..."

class PlanEvent(Event):
    query:str
    type:str
    
class SectionGenerationEvent(Event):
    report: Report


class ResearchReportEvent(Event):
    report: Report

# 添加 ProgressEvent 类用于报告进度
class ProgressEvent(Event):
    msg: str

class PolicyDecisionWorkflow(Workflow):
    def __init__(self, llm, tavily_api_key: str = None, verbose: bool = False):
        super().__init__(timeout=300, verbose=verbose)
        self.llm = llm
        self.tavily_client = AsyncTavilyClient(api_key=tavily_api_key) if tavily_api_key else None
        self.messages = [
            ChatMessage(role="system", content=prompts.agent_prompt)
        ]
        self.thinking_llm = DeepSeek(model="deepseek-reasoner", api_key=os.environ.get("DEEPSEEK_API_KEY",os.getenv("DEEPSEEK_API_KEY")))

        try:
            self.rag_client = RAGClient(llm=self.llm)
            print("RAG client initialized successfully")
        except Exception as e:
            print(f"Error initializing RAG client: {e}")
            self.rag_client = None
    
    @step
    async def identify_question_type(self, ev: StartEvent, ctx: Context) -> PlanEvent | StopEvent:
        """Identify the type of query and return the appropriate event"""
        try:
            query = ev.input.strip()
            self.save_messages(self.messages, ChatMessage(role="user", content=query))
            chat_response = self.thinking_llm.chat(self.messages)
            response = chat_response.message.content and chat_response.message.content.strip()
            print(response)
            if "research" == response:
                print("type:", "research")
                return PlanEvent(query=query,type="research")
            else:
                print("type:", "chat")
                self.save_messages(self.messages, ChatMessage(role="assistant", content=response))
                return StopEvent(result=response)

        except Exception as e:
            print(f"Error identifying query type: {e}")
            self.save_messages(self.messages, ChatMessage(role="assistant", content=SERVICE_UNAVAILABLE))
            return StopEvent(result=SERVICE_UNAVAILABLE)
        
    @step
    async def generate_report_plan(self, ctx: Context, ev: PlanEvent) -> SectionGenerationEvent:
        """Step 1: Generate report plan based on query"""
        max_retries = 2
        retry_count = 0
        await self.rag_client.find_collection(topic=ev.query)
        ctx.write_event_to_stream(ProgressEvent(msg="### 开始生成报告计划 \n"))
        prompt = prompts.report_planner_instructions.format(topic=ev.query)
        while retry_count <= max_retries:
            try:
                response = self.llm.complete(prompt, response_format={"type": "json_object"})
                parsed_data = json.loads(response.text)
                report_data = parsed_data["report"]
                all_data = []
                all_data.append(report_data["introduction"])
                all_data.extend(report_data["mainBodySections"])
                all_data.append(report_data["conclusion"])
                report = Report(sections=[Section(**section) for section in all_data])
                sections_contents = "\n - ".join([s["description"] for s in all_data])
                ctx.write_event_to_stream(ProgressEvent(msg="\n"+sections_contents))

                return SectionGenerationEvent(report=report)

            except Exception as e:
                retry_count += 1
                print(f"Error generating report plan (attempt {retry_count}/{max_retries}): {e}")
                if retry_count > max_retries:
                    self.save_messages(self.messages, ChatMessage(role="assistant", content=SERVICE_UNAVAILABLE))
                    return StopEvent(result=SERVICE_UNAVAILABLE)
                await asyncio.sleep(1)  # 在重试之前等待1秒

    @step
    async def generate_sections(self, ctx: Context, ev: SectionGenerationEvent) -> ResearchReportEvent:
        """Step 2: Generate sections based on report plan"""
        try:
            # 发送进度事件
            ctx.write_event_to_stream(ProgressEvent(msg="\n ### 开始生成报告章节 \n"))
            async def generate_single_section(section):
                if section.research and not section.content:
                    queries = await self.generate_queries(section.description,QUERY_GENERATION_COUNT)
                    results = await self.perform_searches(queries,tavily_search_criteria={"search_depth": "basic", "max_results": MAX_SEARCH_COUNT})
                    prompt = prompts.section_writer_instructions.format(section_topic=section.description,context=results)
                    
                    # 使用流式LLM响应
                    try:
                        response = self.llm.complete(prompt)
                        ctx.write_event_to_stream(ProgressEvent(msg=response.text))
                        return section, response.text
                    except Exception as e:
                        print(f"Error streaming LLM response: {e}")
                return section, section.content
            
            # Process sections in parallel
            section_tasks = [generate_single_section(section) for section in ev.report.sections]
            section_results = await asyncio.gather(*section_tasks)
            
            # Update sections with generated content
            for section, content in section_results:
                section.content = content
            
            # print(datetime.now().strftime("%Y-%m-%d %H:%M:%S"),f"查询时间结束")

            # 发送进度事件
            ctx.write_event_to_stream(ProgressEvent(msg="### 所有章节生成完成 \n\n"))
            
            return ResearchReportEvent(report=ev.report)
        except Exception as e:
            print(f"Error generating sections: {e}")
            # Return a default query if there's an error
            self.save_messages(self.messages, ChatMessage(role="assistant", content=SERVICE_UNAVAILABLE))
            return StopEvent(result=SERVICE_UNAVAILABLE)
    
    async def generate_queries(self, query: str,count=1) -> List[str]:
        """ Generate search queries based on the proposition"""
        try:
            # Use LLM to generate queries
            prompt = prompts.search_query_prompt.format(query=query)
            response = self.llm.complete(prompt)
            queries = [q.strip() for q in response.text.split(",")]
            if queries and len(queries) > count:
                queries = queries[:count]
            return queries
        except Exception as e:
            print(f"Error generating queries: {e}")
            # Return a default query if there's an error
            return []

    async def perform_searches(self, queries: List[str],tavily_search_criteria={}) -> Dict[str, str]:
        """Perform parallel searches for each query"""
        try:
            results = {}
            content = []
            search_results = []
            for query in queries:

                # First try to search in knowledge base collections
                try:
                    # Search in knowledge base
                    kb_results = await self.rag_client.search_knowledge_base(query)
                    
                    # If we found results in the knowledge base, return them
                    if kb_results:
                        content.append(kb_results)
                except Exception as e:
                    print(f"Error searching knowledge base: {e}")
                # Second try to search in Tavily
                try:
                    result = await self.tavily_client.search(query, search_depth=tavily_search_criteria.get("search_depth", "advanced"), max_results=tavily_search_criteria.get("max_results"))
                    # Extract relevant information from Tavily response
                    if isinstance(result, dict) and 'results' in result:
                        for item in result['results']:
                            if 'score' in item and item['score'] > 0.6:
                                # Clean up unnecessary fields and ensure proper encoding
                                cleaned_item = {
                                    'title': item.get('title', ''),
                                    'url': item.get('url', ''),
                                    'content': self.decode_text(item.get('content', '')),
                                }
                                content.append(json.dumps(cleaned_item, ensure_ascii=False))
                except Exception as e:
                    print(f"Error searching Tavily: {e}")
            
                if content:
                    search_results.append((query, "\n".join(content)))

            # Process results
            for query, result in search_results:
                if result:
                    results[query] = result
            
            return results
        except Exception as e:
            print(f"Error performing searches: {e}")
            # Return simulated results if there's an error
            return {}  
                
    def generate_chart_data(self, report):
        """Generate chart data based on the report content"""
        try:
            # 使用LLM从报告内容中提取图表数据
            json_example = """
            {
            "bar_chart": {
                "tab_title": "政策影响分析",
                "title": "主题有关的描述",
                "description": "各领域政策影响程度评估",
                "data": [
                {"category": "经济", "impact": 85, "potential": 90},
                {"category": "社会", "impact": 75, "potential": 80},
                {"category": "环境", "impact": 60, "potential": 70},
                {"category": "科技", "impact": 90, "potential": 95},
                {"category": "教育", "impact": 70, "potential": 85}
                ],
                "xAxisDataKey": "category",
                "series": [
                {"dataKey": "impact", "name": "当前影响"},
                {"dataKey": "potential", "name": "潜在影响"}
                ]
            },
            "line_chart": {
                "tab_title": "政策趋势分析",
                "title": "主题有关的描述",
                "description": "近年政策发展趋势",
                "data": [
                {"year": "2019", "policies": 45, "implementations": 30},
                {"year": "2020", "policies": 52, "implementations": 35},
                {"year": "2021", "policies": 58, "implementations": 42},
                {"year": "2022", "policies": 70, "implementations": 55},
                {"year": "2023", "policies": 85, "implementations": 65},
                {"year": "2024", "policies": 95, "implementations": 75}
                ],
                "xAxisDataKey": "year",
                "series": [
                {"dataKey": "policies", "name": "政策数量"},
                {"dataKey": "implementations", "name": "实施数量"}
                ]
            },
            "pie_chart": {
                "tab_title": "政策类型分布",
                "title": "主题有关的描述",
                "description": "不同类型政策的占比分析",
                "data": [
                {"name": "经济政策", "value": 35},
                {"name": "社会政策", "value": 25},
                {"name": "科技政策", "value": 20},
                {"name": "环境政策", "value": 15},
                {"name": "其他政策", "value": 5}
                ]
            },
            "radar_chart": {
                "tab_title": "政策综合评估",
                "title": "主题有关的描述",
                "description": "政策各维度评估分析",
                "data": [
                {"subject": "经济效益", "current": 80, "target": 90},
                {"subject": "社会影响", "current": 70, "target": 85},
                {"subject": "实施难度", "current": 60, "target": 50},
                {"subject": "创新程度", "current": 85, "target": 95},
                {"subject": "可持续性", "current": 75, "target": 90}
                ],
                "nameKey": "subject",
                "series": [
                {"dataKey": "current", "name": "当前水平"},
                {"dataKey": "target", "name": "目标水平"}
                ]
            }
            }
            """
            
            prompt = f"""
            你是一个数据分析专家，需要从以下政策报告中提取关键数据并生成多种图表数据。
            请分析报告内容，提取或推断相关数据，并生成以下五种图表的数据：

            报告内容：
            {report}

            请根据报告生成以下图表数据（必须包含所有五种图表）：

            1. 柱状图（bar_chart）：展示政策影响分析，包括各领域政策影响程度评估
            2. 折线图（line_chart）：展示政策趋势分析，包括近年政策发展趋势
            3. 饼图（pie_chart）：展示政策类型分布，包括不同类型政策的占比分析
            4. 雷达图（radar_chart）：展示政策综合评估，包括政策各维度评估分析

            请按照以下JSON格式返回数据（不要包含任何其他文本，只返回JSON对象）：

            ```json
            {json_example}
            ```
            ## note
            - 请确保生成的数据与报告内容相关，并且数据格式正确。如果报告中没有明确的数据，请根据报告内容合理推断。
            - tab_title 要求：使用例子的标题作为tab_title。
            - title 要求：请根据每个chart内容生成有意义的标题，需要和报告内容相关。
            - description 要求：请根据每个chart内容生成有意义的描述，需要和报告内容相关。
            """

            # 使用LLM生成图表数据
            response = self.llm.complete(prompt,response_format={"type": "json_object"})
            
            # 尝试解析JSON
            chart_data = None
            
            try:
                chart_data = json.loads(response.text)
                print("成功直接解析LLM响应为JSON")
            except Exception as e:
                print(f"直接解析LLM响应为JSON失败: {e}")

            
            # 如果无法解析JSON，使用默认图表数据
            if not chart_data:
                print("无法解析LLM响应，使用默认图表数据")
                chart_data = self.get_default_chart_data()
            
            return chart_data
            
        except Exception as e:
            print(f"Error generating chart data: {e}")
            # 如果出现任何错误，返回默认图表数据
            return self.get_default_chart_data()
    
    def get_default_chart_data(self):
        """Return default chart data as fallback"""
        return {
            "bar_chart": {
                "title": "政策影响分析",
                "description": "各领域政策影响程度评估",
                "data": [
                    {"category": "经济", "impact": 85, "potential": 90},
                    {"category": "社会", "impact": 75, "potential": 80},
                    {"category": "环境", "impact": 60, "potential": 70},
                    {"category": "科技", "impact": 90, "potential": 95},
                    {"category": "教育", "impact": 70, "potential": 85}
                ],
                "xAxisDataKey": "category",
                "series": [
                    {"dataKey": "impact", "name": "当前影响"},
                    {"dataKey": "potential", "name": "潜在影响"}
                ]
            },
            "line_chart": {
                "title": "政策趋势分析",
                "description": "近年政策发展趋势",
                "data": [
                    {"year": "2019", "policies": 45, "implementations": 30},
                    {"year": "2020", "policies": 52, "implementations": 35},
                    {"year": "2021", "policies": 58, "implementations": 42},
                    {"year": "2022", "policies": 70, "implementations": 55},
                    {"year": "2023", "policies": 85, "implementations": 65},
                    {"year": "2024", "policies": 95, "implementations": 75}
                ],
                "xAxisDataKey": "year",
                "series": [
                    {"dataKey": "policies", "name": "政策数量"},
                    {"dataKey": "implementations", "name": "实施数量"}
                ]
            },
            "pie_chart": {
                "title": "政策类型分布",
                "description": "不同类型政策的占比分析",
                "data": [
                    {"name": "经济政策", "value": 35},
                    {"name": "社会政策", "value": 25},
                    {"name": "科技政策", "value": 20},
                    {"name": "环境政策", "value": 15},
                    {"name": "其他政策", "value": 5}
                ]
            },
            "radar_chart": {
                "title": "政策综合评估",
                "description": "政策各维度评估分析",
                "data": [
                    {"subject": "经济效益", "current": 80, "target": 90},
                    {"subject": "社会影响", "current": 70, "target": 85},
                    {"subject": "实施难度", "current": 60, "target": 50},
                    {"subject": "创新程度", "current": 85, "target": 95},
                    {"subject": "可持续性", "current": 75, "target": 90}
                ],
                "nameKey": "subject",
                "series": [
                    {"dataKey": "current", "name": "当前水平"},
                    {"dataKey": "target", "name": "目标水平"}
                ]
            }
        }     
    @step
    async def format_final_report(self, ctx: Context, ev: ResearchReportEvent) -> StopEvent:
        """Final step: Format and return the complete research report"""
        try:
            # 发送进度事件
            ctx.write_event_to_stream(ProgressEvent(msg="### 正在生成最终报告 \n"))
            
            sections_contents = "".join([s.content for s in ev.report.sections])
            prompt = prompts.final_section_writer_instructions.format(context=sections_contents)
            
            # 使用流式LLM响应
            try:
                response = self.llm.complete(prompt)
                result = response.text.replace('[section]',sections_contents)
            except Exception as e:
                print(f"Error streaming LLM response: {e}")
            # 生成图表数据
            try:
                # 示例图表数据 - 在实际应用中，这些数据应该从报告内容中提取或生成
                chart_data = self.generate_chart_data(result)
                # chart_data =None
                # 将图表数据和报告内容合并为一个JSON响应
                response_data = {
                    "content": result,
                    "charts": chart_data
                }
                
                # 转换为JSON字符串
                result_json = json.dumps(response_data, ensure_ascii=False)
                self.save_messages(self.messages, ChatMessage(role="assistant", content=result_json))  

                return StopEvent(result=result_json)
            except Exception as e:
                print(f"Error generating chart data: {e}")
                # 如果图表数据生成失败，仍然返回文本内容
                return StopEvent(result=result)
        except Exception as e:
            print(f"Error generating queries: {e}")
            self.save_messages(self.messages, ChatMessage(role="assistant", content=SERVICE_UNAVAILABLE))
            # Return a default query if there's an error
            return StopEvent(result=SERVICE_UNAVAILABLE)

    def decode_text(self,text):
        """Decode Chinese text that might be Latin-1 encoded"""
        try:
            text= text.encode('latin1').decode('utf-8')
            return text[:500]
        except Exception as e:
            # print(f"Error decoding text: {e}")
            return text[:500]
    
    def save_messages(self, messages, message, max_length=10):
        if len(messages) > max_length + 1:
            system_msg = messages[0] if messages[0].role == "system" else None
            recent_msgs = messages[-max_length:]
            messages[:] = [system_msg] + recent_msgs if system_msg else recent_msgs
        messages.append(message)

async def main():
    topic = "中国人工智能"
    workflow = PolicyDecisionWorkflow(
        llm=OpenRouter(
            api_key=os.getenv("OPEN_ROUTER_API_KEY"),
            model="google/gemini-2.0-flash-lite-preview-02-05:free",
            max_tokens=48096,
            context_window=1000000,
        ),
        # llm=Ollama(model="qwen2.5", request_timeout=60.0),
        tavily_api_key=os.getenv("TAVILY_API_KEY"),
        verbose=True
    )
    
    handler = workflow.run(input=topic)
    
    # 打印所有进度事件
    async for event in handler.stream_events():
        if isinstance(event, ProgressEvent):
            print(event.msg)
    
    # 获取最终结果
    final_result = await handler
    print("最终结果:", final_result)

# Example usage
if __name__ == "__main__":
    asyncio.run(main())
