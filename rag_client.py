"""
RAG Client for interacting with Weaviate knowledge base
"""
import json
import requests
from typing import List, Dict, Any, Optional
import weaviate
from weaviate.classes.query import MetadataQuery
from weaviate.classes.config import Configure
import os

class RAGClient:
    """
    Client for interacting with Weaviate knowledge base using REST API
    """
    def __init__(self, llm=None):
        """
        Initialize the RAG client
        
        Args:
            base_url: Base URL of the Weaviate server
        """
        # Use environment variable if available, otherwise default to localhost
        import os
        self.base_url = os.environ.get("WEAVIATE_URL", "http://localhost:8080")
        self.llm = llm
        self.collections = None

    async def find_collection(self, topic: str) -> Optional[Dict[str, str]]:
        """
        Find a collection by name in the knowledge base
        
        Args:
            topic: Topic to find collection for
            
        Returns:
            Collection information or None if not found
        """
        try:
            # Get all collections
            collections = await self.list_collections()
            if not collections:
                return None
                
            # Find relevant collections
            relevant_collections = await self.find_relevant_collections(topic, collections)
            if not relevant_collections:
                return None
            self.collections = relevant_collections
        except Exception as e:
            print(f"Error finding collections: {e}")
            return None

    async def list_collections(self) -> List[Dict[str, str]]:
        """
        List all collections in the knowledge base
        
        Returns:
            List of collections with name and description
        """
        try:
            response = requests.get(f"{self.base_url}/v1/schema")
            if not response.ok:
                print(f"Error listing collections: {response.text}")
                return []
                
            data = response.json()
            if "classes" not in data:
                return []
                
            return [
                {
                    "name": c["class"],
                    "description": c.get("description", "")
                }
                for c in data["classes"]
            ]
        except Exception as e:
            print(f"Error listing collections: {e}")
            return []
            
    async def check_and_configure_vectorizer(self, collection_name: str, model: str = "nomic-embed-text") -> bool:
        """
        Check if a collection has a vectorizer configured and configure one if needed
        
        Args:
            collection_name: Name of the collection to check
            model: Ollama model to use for vectorization (default: nomic-embed-text)
            
        Returns:
            True if the collection has a vectorizer configured or was successfully configured, False otherwise
        """
        try:
            # Get the collection schema to check if it has a vectorizer
            response = requests.get(f"{self.base_url}/v1/schema/{collection_name}")
            if not response.ok:
                print(f"Error getting collection schema: {response.text}")
                return False
                
            data = response.json()
            
            # Check if the collection has a vectorizer configured
            if "vectorConfig" in data and data["vectorConfig"] is not None:
                print(f"Collection {collection_name} already has a vectorizer configured: {data['vectorConfig']}")
                return True
            
        except Exception as e:
            print(f"Error checking or configuring vectorizer: {e}")
            return False

    async def search_collection(self, collection_name: str, query: str, limit: int = 1, alpha: float = 0.6, return_metadata: bool = False) -> List[Dict[str, Any]]:
        """
        Search for documents in a collection using hybrid search
        
        Args:
            collection_name: Name of the collection to search
            query: Query string
            limit: Maximum number of results to return
            alpha: Balance between keyword and vector search (0 = pure keyword, 1 = pure vector)
            return_metadata: Whether to return additional metadata about the results
            
        Returns:
            List of documents matching the query
        """
        try:            
            # Use the Weaviate Python client for hybrid search
            client = weaviate.connect_to_local(os.environ.get("RAG_HOST", "localhost"))  # Assuming Weaviate is running locally
                # Get the collection
            collection = client.collections.get(collection_name)
            
            # Set up metadata query based on return_metadata flag
            metadata_query = MetadataQuery(
                certainty=True
            )
            
            # Perform hybrid search
            response = collection.query.near_text(
                query=query,
                limit=limit,
                return_metadata=metadata_query,
            )
            
            # Process the results
            results = []
            for obj in response.objects:
                result = {
                    "title": obj.properties.get("title", ""),
                    "url": obj.properties.get("url", ""),
                    "content": obj.properties.get("content", "")if obj.properties.get("content") else "",
                }
                if obj.metadata.certainty>0.85:
                    results.append(result)
            return results
    
        except Exception as e:
            print(f"Error searching collection: {e}")
            return []
        finally:
            # Always close the client to free up resources
            client.close()

    async def find_relevant_collections(self, query: str, collections: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Use LLM to determine which collections are relevant to the query
        
        Args:
            llm: Language model to use
            query: Query string
            collections: List of collections with name and description
            
        Returns:
            List of collections that are relevant to the query
        """
        if not collections:
            return []
            
        collections_info = "\n".join([
            f"Collection: {c['name']}, Description: {c['description']}"
            for c in collections
        ])
        
        prompt = f"""
        You are an AI assistant helping to determine which knowledge base collections are relevant to a user query.
        
        User Query: {query}
        
        Available Collections:
        {collections_info}
        
        Based on the query and collection descriptions, list the names of the collections that are most relevant to the query.
        Only include collections that have a high likelihood of containing information related to the query.
        ## notes
        - Return your answer as a comma-separated list of collection names, without any additional text.
        -If no collections seem relevant, return an empty string.
        """
        
        # prompt = f"""
        # You are an AI assistant tasked with identifying relevant knowledge base collections for a user query.
        
        # User Query: {query}
        
        # Available Collections:
        # {collections_info}
        
        # ## Instructions:
        # 1.  Examine the user query and the description of each available knowledge base collection.
        # 2.  Determine if the collection's description contains information that could directly help answer the user's query. A strong match is required. Check to see if keywords related to the topic are found in the collection descriptions. 
        # 3.  If a collection's description has no indication of content that will help with the query or is only tangentially related, *do not* include it in your answer.
        
        # ## Output:
        # - Return your answer as a comma-separated list of collection names, without any additional text.
        # """
        
        try:
            response = self.llm.complete(prompt)
            collection_names = [name.strip() for name in response.text.split(",") if name.strip()]
            
            # Filter to only include collections that actually exist
            existing_collection_names = [c["name"] for c in collections]
            relevant_collections = [
                c for c in collections 
                if c["name"] in collection_names and c["name"] in existing_collection_names
            ]
            
            return relevant_collections
        except Exception as e:
            print(f"Error finding relevant collections: {e}")
            return []

    async def search_knowledge_base(self, query: str) -> Optional[str]:
        """
        Search the knowledge base for documents matching the query
        
        Args:
            query: Query string
            
        Returns:
            String containing the search results, or None if no results were found
        """
        try:      
            # Search in relevant collections
            if not self.collections:
                return None
            all_results = []
            for collection in self.collections:
                collection_name = collection["name"]
                results = await self.search_collection(collection_name, query)
                all_results.extend(results)
                
            if not all_results:
                return None
                
            # Format results as a string
            formatted_results = []
            for result in all_results:
                formatted_result = json.dumps(result, ensure_ascii=False)
                formatted_results.append(formatted_result)
                
            return "\n".join(formatted_results)
        except Exception as e:
            print(f"Error searching knowledge base: {e}")
            return None
