"""
Policy Decision Agent - Interactive command-line tool for policy decisions
"""

from llama_index.core.agent import FunctionCallingAgent
from llama_index.llms.ollama import Ollama
from llama_index.llms.openrouter import OpenRouter
import asyncio
import os
import sys
from dotenv import load_dotenv
from api import policy_decision_tool

# Load environment variables
load_dotenv()

# Create a system prompt for the agent
system_prompt = """You are an intelligent policy advisor that helps users understand complex policy topics.
You have access to a policy decision generator tool that can create comprehensive reports on various policy topics.
When asked about a policy topic, use the policy_decision_generator tool to create a detailed analysis.
Always provide a concise summary of the policy decision report to the user.
## Output
chinese
"""

class PolicyAgent:
    def __init__(self):
        # Set up the LLM
        # You can use <PERSON>llama for local models
        self.llm = Ollama(model="qwen2.5")  # Replace with your model name
        
        # Create the agent with the policy_decision_tool
        self.agent = FunctionCallingAgent.from_tools(
            tools=[policy_decision_tool],
            llm=self.llm,
            verbose=True,
            system_prompt=system_prompt,
        )
    
    async def process_message(self, message):
        """Process a user message and return the agent's response"""
        try:
            # Get response from agent
            response = await self.agent.aquery(
                message
            )
            return str(response)
        except Exception as e:
            error_message = f"Error: {str(e)}"
            print(error_message)
            return error_message
    
    def print_help(self):
        """Print help information"""
        help_text = """
Available commands:
- /help: Show this help message
- /clear: Clear chat history
- /exit: Exit the application
- /history: Show chat history
- Any other input will be sent to the agent as a query
"""
        return help_text

async def main():
    """Run the interactive policy decision agent"""
    try:
        print("Initializing Policy Decision Agent...")
        agent = PolicyAgent()
        print("\n" + "="*50)
        print("Policy Decision Agent - Interactive CLI")
        print("="*50)
        print("Type '/help' for available commands or '/exit' to quit")
        print("Type your query and press Enter to get a response")
        print("="*50 + "\n")
        
        while True:
            # Get user input
            user_input = input("\n> ")
            
            # Process special commands
            if user_input.lower() == '/exit':
                print("Exiting. Goodbye!")
                break
            elif user_input.lower() == '/help':
                print(agent.print_help())
                continue
            elif not user_input.strip():
                continue
            
            # Process regular message
            print("\nProcessing your request. This may take a moment...")
            response = await agent.process_message(user_input)
            print(f"\nAGENT: {response}")
            
    except KeyboardInterrupt:
        print("\nExiting due to user interrupt. Goodbye!")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
