<!DOCTYPE html>
<html>
<head>
    <title>PocketFlow Chat</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .chat-container { 
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            width: 100%;
            max-width: 600px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .status {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .messages { 
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .message { 
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 15px;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .user-message { 
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }
        
        .ai-message { 
            background: #f1f3f4;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }
        
        .input-container { 
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            gap: 12px;
        }
        
        #messageInput { 
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 25px;
            background: white;
            font-size: 15px;
            outline: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #messageInput::placeholder {
            color: #999;
        }
        
        #sendButton { 
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            transition: all 0.2s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #sendButton:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        #sendButton:disabled { 
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .messages::-webkit-scrollbar {
            width: 6px;
        }
        
        .messages::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .messages::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="header">
            <h1>PocketFlow Chat</h1>
            <div class="status" id="status">Connecting...</div>
        </div>
        
        <div class="messages" id="messages"></div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message..." disabled>
            <button id="sendButton" disabled>Send</button>
        </div>
    </div>

    <script>
        const ws = new WebSocket(`ws://localhost:8000/ws`);
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusDiv = document.getElementById('status');
        
        let isStreaming = false;
        let currentAiMessage = null;

        ws.onopen = function() {
            statusDiv.textContent = 'Connected';
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'start') {
                isStreaming = true;
                currentAiMessage = document.createElement('div');
                currentAiMessage.className = 'message ai-message';
                messagesDiv.appendChild(currentAiMessage);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
                sendButton.disabled = true;
                statusDiv.textContent = 'AI is typing...';
                
            } else if (data.type === 'chunk') {
                if (currentAiMessage) {
                    currentAiMessage.textContent += data.content;
                    messagesDiv.scrollTop = messagesDiv.scrollHeight;
                }
                
            } else if (data.type === 'end') {
                isStreaming = false;
                currentAiMessage = null;
                sendButton.disabled = false;
                statusDiv.textContent = 'Connected';
                messageInput.focus();
            }
        };

        ws.onclose = function() {
            statusDiv.textContent = 'Disconnected';
            messageInput.disabled = true;
            sendButton.disabled = true;
        };

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message && !isStreaming) {
                const userMessage = document.createElement('div');
                userMessage.className = 'message user-message';
                userMessage.textContent = message;
                messagesDiv.appendChild(userMessage);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;

                ws.send(JSON.stringify({
                    type: 'message',
                    content: message
                }));

                messageInput.value = '';
                statusDiv.textContent = 'Sending...';
            }
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html> 