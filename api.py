from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import logging
import os
import sys
from dotenv import load_dotenv
from typing import Optional, Dict, Any
import time

from policy_decision_workflow import PolicyDecisionWorkflow, ProgressEvent, StopEvent
# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    stream=sys.stdout)
logger = logging.getLogger(__name__)
# logging.basicConfig(filename="llamaindex.log", level=logging.DEBUG)
# Load environment variables
load_dotenv()
SESSION_TIMEOUT = 600  # 10分钟

app = FastAPI(title="Policy Decision API", description="API for generating policy decisions using SSE")

# 设置LLM
from llama_index.llms.deepseek import DeepSeek

# you can also set DEEPSEEK_API_KEY in your environment variables
llm = DeepSeek(model="deepseek-chat", api_key=os.environ.get("DEEPSEEK_API_KEY",os.getenv("DEEPSEEK_API_KEY")))

TAVILY_API_KEY = os.environ.get("TAVILY_API_KEY",os.getenv("TAVILY_API_KEY"))
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)
conversation_contexts: Dict[str, Dict[str, Any]] = {}

async def sse_generator(queue: asyncio.Queue):
    """Generate SSE events from the queue"""
    try:
        while True:
            event = await queue.get()
            if event is None:
                break
                
            # Format as SSE
            sse_data = f"event: {event['event']}\ndata: {json.dumps(event['data'])}\n\n"
            logger.debug(f"Sending SSE data: {sse_data}")
            yield sse_data
    except asyncio.CancelledError:
        # Handle client disconnection
        logger.info("Client disconnected")
    except Exception as e:
        logger.error(f"Error in SSE generator: {e}")

# 创建一个Agent的SSE工作流
class AgentSSEWorkflow():
    """Extension of FunctionCallingAgent that sends events to an SSE queue"""
    # 存储所有会话的上下文
    conversation_contexts = {}
    def __init__(self, queue: asyncio.Queue, verbose: bool = True):
        self.queue = queue
        self.verbose = verbose
        self.workflow = PolicyDecisionWorkflow(llm=llm, tavily_api_key=TAVILY_API_KEY, verbose=self.verbose)
        self.last_accessed = time.time()

    async def run(self, query: str):
        """Run the agent and send events to the queue"""
        try:
            logger.info(f"Starting agent workflow for query: {query}")
            
            # 运行工作流
            handler = self.workflow.run(input=query)
            # 流式返回事件
            async for event in handler.stream_events():
                if isinstance(event, ProgressEvent):
                    # 处理进度事件
                    await self.queue.put({"event": "progress", "data": event.msg})
                elif isinstance(event, StopEvent):
                    # 工作流完成
                    logger.info("工作流完成，最终结果: " + event.result[:100] + "...")
                    await self.queue.put({"event": "complete", "data": event.result})

        except Exception as e:
            # 发送错误事件
            logger.error(f"Error in agent workflow: {str(e)}")
            await self.queue.put({"event": "error", "data": "服务器出现故障,请稍后再试"})
        finally:
            # 结束流
            await self.queue.put(None)

async def cleanup_expired_sessions():
    """清理超时的会话"""
    current_time = time.time()
    expired_sessions = []
    
    for session_id, workflow in conversation_contexts.items():
        if current_time - workflow.last_accessed > SESSION_TIMEOUT:
            expired_sessions.append(session_id)
    
    for session_id in expired_sessions:
        logger.info(f"Removing expired session: {session_id}")
        del conversation_contexts[session_id]
        
@app.get("/api/policy/agent/stream")
async def stream_agent_response(
    query: Optional[str] = None,
    conversationId: str = None,
):
    """Stream agent responses using SSE"""
    try:
        # 参数验证
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")
        if not conversationId:
            raise HTTPException(status_code=400, detail="Conversation ID is required")
        
        asyncio.create_task(cleanup_expired_sessions())

        # 获取或创建会话上下文
        if conversationId not in conversation_contexts:
            queue = asyncio.Queue()
            # 创建工作流
            workflow = AgentSSEWorkflow(queue=queue)
            conversation_contexts[conversationId] = workflow
        else:
            workflow = conversation_contexts[conversationId]
            # 更新最后访问时间
            workflow.last_accessed = time.time()
            
        # 创建任务运行工作流
        asyncio.create_task(workflow.run(query))
        
        # 返回流式响应
        return StreamingResponse(
            sse_generator(workflow.queue),
            media_type="text/event-stream"
        )

    except Exception as e:
        logger.error(f"Unhandled error in stream_agent_response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/policy/search")
async def search_topic(topic: str):
    """Generate search queries and perform searches for a given topic"""
    try:  
        # Create workflow instance just to use its methods
        workflow = PolicyDecisionWorkflow(
            llm=llm,
            tavily_api_key=TAVILY_API_KEY,
            verbose=True
        )
        
        # Generate queries
        queries = await workflow.generate_queries(topic,3)
        if queries:  
            # Perform searches
            search_results = await workflow.perform_searches(queries, tavily_search_criteria={"search_depth": "advanced", "max_results": 5})
            
            # Return combined results
            return {
                "search_results": search_results
            }
    except Exception as e:
        logger.error(f"Error in search_topic: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)
