from deepeval import assert_test
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from deepeval.metrics import GEval
from policy_decision_workflow import PolicyDecisionWorkflow, StartEvent, Context
import pytest
import asyncio
from llama_index.llms.deepseek import DeepSeek
import os
from deepseek import DeepSeekLLM
deepseek_llm = DeepSeekLLM(model_name="deepseek-chat", api_key="***********************************")

# Initialize DeepSeek LLM
llm = DeepSeek(model="deepseek-chat", api_key=os.environ.get("DEEPSEEK_API_KEY"))

@pytest.mark.asyncio
async def test_identify_question_type_research():
    """测试识别研究类型的查询"""
    input = "这是一个研究性质的问题：中国新能源政策发展趋势如何？"
    
    workflow = PolicyDecisionWorkflow(llm=llm)
    start_event = StartEvent(input=input)
    ctx = Context(workflow=workflow)
    
    result = await workflow.identify_question_type(start_event, ctx)
    
    test_case = LLMTestCase(
        input=input,
        actual_output=result.type,
        expected_output="research"
    )

    # Create evaluation metric using DeepSeek
    correctness_metric = GEval(
        name="查询类型识别准确性",
        criteria="判断方法是否正确识别出查询类型为research",
        evaluation_params=[LLMTestCaseParams.ACTUAL_OUTPUT, LLMTestCaseParams.EXPECTED_OUTPUT],
        threshold=0.8,
        model=deepseek_llm,
        verbose_mode=True
    )
    

    assert result.__class__.__name__ == "PlanEvent"
        
    assert_test(test_case, [correctness_metric])

@pytest.mark.asyncio
async def test_identify_question_type_chat():
    """测试识别聊天类型的查询"""
    input = "你好，请问今天天气怎么样？"
    workflow = PolicyDecisionWorkflow(llm=llm)
    start_event = StartEvent(input=input)
    ctx = Context(workflow=workflow)
    result = await workflow.identify_question_type(start_event, ctx)
    # print(result)
    
    test_case = LLMTestCase(
        input=input,
        actual_output=result.result,
        expected_output="chat"
    )
    
    correctness_metric = GEval(
        name="查询类型识别准确性",
        criteria="check if actual result is not 'research'",
        evaluation_params=[LLMTestCaseParams.ACTUAL_OUTPUT, LLMTestCaseParams.EXPECTED_OUTPUT],
        threshold=0.8,
        model=deepseek_llm,
        verbose_mode=True
    )
    

    assert result.__class__.__name__ == "StopEvent"
    assert isinstance(result.result, str)
    
    assert_test(test_case, [correctness_metric])

