import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
import asyncio
from policy_decision_workflow import PolicyDecisionWorkflow, StartEvent, Context, PlanEvent, StopEvent
from llama_index.core.llms import ChatMessage, ChatResponse

# 创建一个模拟的 LLM 类
class MockLLM:
    def __init__(self, response_content):
        self.response_content = response_content
    
    def chat(self, *args, **kwargs):
        # 注意：这里不是异步方法，直接返回 ChatResponse 对象
        return ChatResponse(message=ChatMessage(role="assistant", content=self.response_content))

@pytest.fixture
def workflow():
    """创建 PolicyDecisionWorkflow 实例"""
    return PolicyDecisionWorkflow(llm=None)  # 传入 None，我们会在测试中替换 thinking_llm

@pytest.mark.asyncio
async def test_identify_question_type_research(workflow, monkeypatch):
    """测试研究类型查询识别，使用 monkeypatch 模拟 LLM"""
    # 创建模拟的 LLM 实例，返回 "research"
    mock_llm = MockLLM("research")
    
    # 替换整个 thinking_llm 对象
    monkeypatch.setattr(workflow, "thinking_llm", mock_llm)
    
    # 创建测试事件和上下文
    start_event = StartEvent(input="中国新能源政策发展趋势如何？")
    ctx = Context(workflow=workflow)
    
    # 执行识别
    result = await workflow.identify_question_type(start_event, ctx)
    
    # 验证结果
    assert isinstance(result, PlanEvent)
    assert result.type == "research"

@pytest.mark.asyncio
async def test_identify_question_type_chat(workflow, monkeypatch):
    """测试聊天类型查询识别，使用 monkeypatch 模拟 LLM"""
    # 创建模拟的 LLM 实例，返回聊天回复
    mock_llm = MockLLM("这是一个聊天回复")
    
    # 替换整个 thinking_llm 对象
    monkeypatch.setattr(workflow, "thinking_llm", mock_llm)
    
    # 创建测试事件和上下文
    start_event = StartEvent(input="你好，请问今天天气怎么样？")
    ctx = Context(workflow=workflow)
    
    # 执行识别
    result = await workflow.identify_question_type(start_event, ctx)
    
    # 验证结果
    assert isinstance(result, StopEvent)
    assert isinstance(result.result, str)
