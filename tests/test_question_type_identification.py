import pytest
import asyncio
from policy_decision_workflow import PolicyDecisionWorkflow, StartEvent, Context, PlanEvent, StopEvent
from llama_index.llms.deepseek import DeepSeek
import os
from deepseek import DeepSeekLLM
import mlflow

# 初始化测试用的 LLM
llm = DeepSeek(model="deepseek-chat", api_key=os.environ.get("DEEPSEEK_API_KEY"))


# mlflow.set_tracking_uri("http://127.0.0.1:5000")
# mlflow.set_experiment("DeepSeek_Experiment")
# mlflow.openai.autolog()

# Enabling tracing for LlamaIndex
mlflow.llama_index.autolog()
# Optional: Set a tracking URI and an experiment
mlflow.set_tracking_uri("http://127.0.0.1:5000")
mlflow.set_experiment("LlamaIndex")

@pytest.fixture
def workflow():
    """创建 PolicyDecisionWorkflow 实例"""
    return PolicyDecisionWorkflow(llm=llm)

@pytest.mark.parametrize("input_query,expected_type,expected_class", [
    ("中国新能源政策发展趋势如何？", "research", PlanEvent),
    # ("你好，请问今天天气怎么样？", "chat", StopEvent),
    # ("分析一下中国经济未来五年的发展前景", "research", PlanEvent),
    # ("你叫什么名字？", "chat", StopEvent),
])
@pytest.mark.asyncio
async def test_identify_question_type_parametrized(workflow, input_query, expected_type, expected_class):
    """参数化测试不同类型的查询识别"""
    # 创建事件和上下文
    start_event = StartEvent(input=input_query)
    ctx = Context(workflow=workflow)
    
    # 执行识别
    result = await workflow.identify_question_type(start_event, ctx)
    
    # 验证结果类型
    assert isinstance(result, expected_class), f"结果应该是 {expected_class.__name__} 类型"
    
    # 验证具体类型
    if expected_class == PlanEvent:
        assert result.type == expected_type, f"查询类型应该是 {expected_type}"
    elif expected_class == StopEvent:
        assert isinstance(result.result, str), "聊天结果应该是字符串"