from deepeval.models import DeepEvalBaseLLM
import requests
from typing import Optional, Type, TypeVar

class DeepSeekLLM(DeepEvalBaseLLM):
    def __init__(self, model_name: str, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        self.model_name = model_name
        self.api_key = api_key
        self.base_url = base_url

    def generate(self, prompt: str) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        payload = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.6,
            "max_tokens": 2048,
        }
        response = requests.post(f"{self.base_url}/chat/completions", json=payload, headers=headers)
        
        if response.status_code == 400:
            # Handle API error gracefully
            print(f"API Error: {response.text}")
            return "Error generating response"
            
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

    async def a_generate(self, prompt: str) -> str:
        # Handle schema parameter but still use synchronous generate
        return self.generate(prompt)

    def get_model_name(self) -> str:
        return self.model_name
        
    def load_model(self) -> None:
        # DeepSeek is an API-based model, no local loading needed
        pass